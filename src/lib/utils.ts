import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(
  amount: number,
  currency: string = "USD",
  locale: string = "en-US"
): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

export function formatNumber(
  num: number,
  locale: string = "en-US",
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(locale, {
    notation: "compact",
    compactDisplay: "short",
    ...options,
  }).format(num);
}

export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(dateObj);
}

export function formatPercent(
  value: number,
  locale: string = "en-US",
  decimals: number = 1
): string {
  return new Intl.NumberFormat(locale, {
    style: "percent",
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
}

export function formatDate(
  date: Date | string | number,
  locale: string = "en-US",
  options?: Intl.DateTimeFormatOptions
): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };

  return new Intl.DateTimeFormat(locale, {
    ...defaultOptions,
    ...options,
  }).format(new Date(date));
}

export function formatRelativeTime(
  date: Date | string | number,
  locale: string = "en-US"
): string {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: "auto" });

  if (Math.abs(diffInSeconds) < 60) {
    return rtf.format(-diffInSeconds, "second");
  } else if (Math.abs(diffInSeconds) < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), "minute");
  } else if (Math.abs(diffInSeconds) < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), "hour");
  } else if (Math.abs(diffInSeconds) < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), "day");
  } else if (Math.abs(diffInSeconds) < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), "month");
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), "year");
  }
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + "...";
}

export function generateId(prefix?: string): string {
  const id = Math.random().toString(36).substr(2, 9);
  return prefix ? `${prefix}-${id}` : id;
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, "$1-$2").toLowerCase();
}

export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
}

// Solar admin specific utilities
export function isLowStock(current: number, minimum: number = 10): boolean {
  return current <= minimum;
}

export function getStockStatus(current: number, minimum: number = 10): {
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
  variant: 'default' | 'warning' | 'destructive';
  label: string;
} {
  if (current === 0) {
    return {
      status: 'out-of-stock',
      variant: 'destructive',
      label: 'Out of Stock'
    };
  }

  if (current <= minimum) {
    return {
      status: 'low-stock',
      variant: 'warning',
      label: 'Low Stock'
    };
  }

  return {
    status: 'in-stock',
    variant: 'default',
    label: 'In Stock'
  };
}

export function getOrderStatus(status: string): {
  variant: 'default' | 'secondary' | 'warning' | 'destructive';
  label: string;
} {
  switch (status.toLowerCase()) {
    case 'completed':
      return { variant: 'default', label: 'Completed' };
    case 'processing':
      return { variant: 'warning', label: 'Processing' };
    case 'pending':
      return { variant: 'secondary', label: 'Pending' };
    case 'cancelled':
      return { variant: 'destructive', label: 'Cancelled' };
    default:
      return { variant: 'secondary', label: capitalize(status) };
  }
}

// Category and slug utilities
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

export function validateSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

export function isValidCategoryName(name: string): boolean {
  return name.trim().length >= 2 && name.trim().length <= 50;
}

export function isValidCategoryDescription(description: string): boolean {
  return description.trim().length >= 10 && description.trim().length <= 500;
}

// Form validation utilities
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateCategoryForm(data: {
  name: string;
  description: string;
  slug: string;
  icon: string;
}): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate name
  if (!data.name.trim()) {
    errors.name = 'Category name is required';
  } else if (!isValidCategoryName(data.name)) {
    errors.name = 'Category name must be between 2 and 50 characters';
  }

  // Validate description
  if (!data.description.trim()) {
    errors.description = 'Description is required';
  } else if (!isValidCategoryDescription(data.description)) {
    errors.description = 'Description must be between 10 and 500 characters';
  }

  // Validate slug
  if (!data.slug.trim()) {
    errors.slug = 'Slug is required';
  } else if (!validateSlug(data.slug)) {
    errors.slug = 'Slug must contain only lowercase letters, numbers, and hyphens';
  }

  // Validate icon
  if (!data.icon.trim()) {
    errors.icon = 'Icon is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

export function calculateGrowth(current: number, previous: number): {
  value: number;
  percentage: string;
  isPositive: boolean;
} {
  if (previous === 0) {
    return {
      value: current,
      percentage: current > 0 ? '+100%' : '0%',
      isPositive: current > 0,
    };
  }

  const growth = ((current - previous) / previous) * 100;
  const isPositive = growth >= 0;

  return {
    value: growth,
    percentage: `${isPositive ? '+' : ''}${growth.toFixed(1)}%`,
    isPositive,
  };
}
