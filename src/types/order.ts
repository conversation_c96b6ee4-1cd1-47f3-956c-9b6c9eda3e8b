export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  productImage?: string;
  sku: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  whatsappNumber?: string;
}

export interface Order {
  id: number;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: 'cash' | 'transfer' | 'card' | 'whatsapp';
  source: 'admin' | 'whatsapp' | 'website';
  whatsappLink?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  deliveryDate?: string;
}

export interface CreateOrderData {
  customerId?: number;
  customerData?: {
    name: string;
    email: string;
    phone: string;
    address: string;
    whatsappNumber?: string;
  };
  items: {
    productId: number;
    quantity: number;
    unitPrice: number;
  }[];
  shipping: number;
  discount: number;
  tax: number;
  paymentMethod?: 'cash' | 'transfer' | 'card' | 'whatsapp';
  notes?: string;
  deliveryDate?: string;
}

export interface OrderFormData {
  customerId: number;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddress: string;
  customerWhatsapp: string;
  items: {
    productId: number;
    quantity: number;
    unitPrice: number;
  }[];
  shipping: number;
  discount: number;
  tax: number;
  paymentMethod: 'cash' | 'transfer' | 'card' | 'whatsapp';
  notes: string;
  deliveryDate: string;
}

export interface OrderFilters {
  search?: string;
  status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'all';
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded' | 'all';
  source?: 'admin' | 'whatsapp' | 'website' | 'all';
  dateFrom?: string;
  dateTo?: string;
  sortBy?: keyof Order;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedOrdersResponse {
  data: Order[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface OrderTableColumn {
  key: keyof Order | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
}

// WhatsApp order format
export interface WhatsAppOrder {
  customerName: string;
  customerPhone: string;
  items: {
    name: string;
    quantity: number;
    price: number;
  }[];
  total: number;
  message: string;
  timestamp: string;
}
