'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  MdNotifications,
  MdSearch,
  <PERSON>d<PERSON><PERSON>,
  MdLogout,
  MdSettings
} from 'react-icons/md';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';

interface HeaderProps {
  title?: string;
  subtitle?: string;
  onMenuClick?: () => void;
  showMenuButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({ title, subtitle, onMenuClick, showMenuButton }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const router = useRouter();
  const { addToast } = useToast();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showUserMenu && !target.closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showUserMenu]);

  const handleLogout = () => {
    // Clear auth data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');

    addToast({
      type: 'success',
      title: 'Logged Out',
      message: 'You have been successfully logged out.',
    });

    // Redirect to login
    router.push('/auth/login');
  };

  const handleSettingsClick = () => {
    router.push('/settings');
    setShowUserMenu(false);
  };

  return (
    <header className="bg-white border-b border-neutral-200 px-3 sm:px-4 lg:px-6 py-3 sm:py-4 shadow-sm">
      <div className="flex items-center justify-between min-w-0 gap-2 sm:gap-4 max-w-full overflow-hidden">
        {/* Left side - Title */}
        <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
          {/* Mobile menu button */}
          {showMenuButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onMenuClick}
              className="lg:hidden flex-shrink-0"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </Button>
          )}

          <div className="min-w-0 flex-1">
            {title && (
              <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-neutral-900 font-display truncate">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-xs sm:text-sm text-neutral-600 mt-1 hidden md:block truncate">{subtitle}</p>
            )}
          </div>
        </div>

        {/* Right side - Compact layout */}
        <div className="flex items-center space-x-2 flex-shrink-0">
          {/* Search - only on larger screens */}
          <div className="hidden lg:block">
            <Input
              type="search"
              placeholder="Search..."
              leftIcon={<MdSearch className="h-4 w-4" />}
              className="w-40 xl:w-48"
            />
          </div>

          {/* Search icon for smaller screens */}
          <div className="lg:hidden">
            <Button variant="ghost" size="sm" className="p-2">
              <MdSearch className="h-5 w-5" />
            </Button>
          </div>

          {/* Notifications */}
          <div className="relative">
            <Button variant="ghost" size="sm" className="relative p-2">
              <MdNotifications className="h-5 w-5" />
              <Badge
                variant="destructive"
                size="sm"
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                3
              </Badge>
            </Button>
          </div>

          {/* User menu - always visible */}
          <div className="relative flex items-center space-x-2 ml-2">
            <div className="hidden sm:block text-right min-w-0 max-w-32">
              <p className="text-sm font-medium text-neutral-900 truncate">
                Admin User
              </p>
              <p className="text-xs text-neutral-600 truncate">
                <EMAIL>
              </p>
            </div>
            <div className="relative user-menu-container">
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 rounded-full p-0 flex-shrink-0"
                onClick={() => setShowUserMenu(!showUserMenu)}
              >
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center shadow-lg">
                  <MdPerson className="h-4 w-4 text-white" />
                </div>
              </Button>

              {/* Dropdown menu */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-1 z-50">
                  <button
                    onClick={handleSettingsClick}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                  >
                    <MdSettings className="h-4 w-4 mr-3" />
                    Settings
                  </button>
                  <hr className="my-1 border-neutral-200" />
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                  >
                    <MdLogout className="h-4 w-4 mr-3" />
                    Sign out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export { Header };
