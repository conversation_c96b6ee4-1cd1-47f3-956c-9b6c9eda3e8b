'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  MdDashboard,
  MdInventory,
  MdCategory,
  MdAnalytics,
  MdSettings,
  MdPeople,
  MdShoppingCart,
  MdWbSunny,
  MdChevronLeft,
  MdChevronRight,
  MdLogout,
} from 'react-icons/md';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/Toast';

interface SidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
  onClose?: () => void;
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: MdDashboard,
  },
  {
    name: 'Products',
    icon: MdInventory,
    href: '/products',
    // children: [
    //   { name: 'All Products', href: '/products', icon: MdInventory },
    //   { name: 'Solar Panels', href: '/products/solar-panels', icon: MdWb<PERSON>unny },
    //   { name: 'Batteries', href: '/products/batteries', icon: MdBattery6Bar },
    //   { name: 'Inverters', href: '/products/inverters', icon: MdElectricBolt },
    //   { name: 'Controllers', href: '/products/controllers', icon: MdMemory },
    // ],
  },
  {
    name: 'Categories',
    href: '/categories',
    icon: MdCategory,
  },
  {
    name: 'Orders',
    href: '/orders',
    icon: MdShoppingCart,
  },
  {
    name: 'Customers',
    href: '/customers',
    icon: MdPeople,
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: MdAnalytics,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: MdSettings,
  },
];

const Sidebar: React.FC<SidebarProps> = ({ collapsed = false, onToggle, onClose }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { addToast } = useToast();
  const [expandedItems, setExpandedItems] = useState<string[]>(['Products']);

  const toggleExpanded = (name: string) => {
    setExpandedItems(prev =>
      prev.includes(name)
        ? prev.filter(item => item !== name)
        : [...prev, name]
    );
  };

  const handleLogout = () => {
    // Clear auth data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');

    addToast({
      type: 'success',
      title: 'Logged Out',
      message: 'You have been successfully logged out.',
    });

    // Redirect to login
    router.push('/auth/login');
  };

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <div
      className={cn(
        'flex h-screen flex-col bg-white border-r border-neutral-200 transition-all duration-300 shadow-sm',
        collapsed ? 'w-16' : 'w-64'
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-neutral-200">
        {!collapsed && (
          <div className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 shadow-lg">
              <MdWbSunny className="h-6 w-6 text-white" />
            </div>
            <span className="text-lg font-bold text-neutral-900 font-display">
              Ecopulse
            </span>
          </div>
        )}
        {collapsed && (
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 shadow-lg mx-auto">
            <MdWbSunny className="h-6 w-6 text-white" />
          </div>
        )}
        {onToggle && (
          <button
            onClick={onToggle}
            className="rounded-lg p-1.5 text-neutral-400 hover:bg-neutral-100 hover:text-neutral-600"
          >
            {collapsed ? (
              <MdChevronRight className="h-5 w-5" />
            ) : (
              <MdChevronLeft className="h-5 w-5" />
            )}
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4 overflow-y-auto">
        {navigation.map((item) => {
          // if (item.children) {
          //   const isExpanded = expandedItems.includes(item.name);
          //   const hasActiveChild = item.children.some(child => isActive(child.href));

          //   return (
          //     <div key={item.name}>
          //       <button
          //         onClick={() => !collapsed && toggleExpanded(item.name)}
          //         className={cn(
          //           'flex w-full items-center rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200',
          //           hasActiveChild
          //             ? 'bg-primary-100 text-primary-700'
          //             : 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900'
          //         )}
          //       >
          //         <item.icon className="h-5 w-5 flex-shrink-0" />
          //         {!collapsed && (
          //           <>
          //             <span className="ml-3 flex-1 text-left">{item.name}</span>
          //             <MdChevronRight
          //               className={cn(
          //                 'h-5 w-5 transition-transform',
          //                 isExpanded && 'rotate-90'
          //               )}
          //             />
          //           </>
          //         )}
          //       </button>
          //       {!collapsed && isExpanded && (
          //         <div className="ml-6 mt-1 space-y-1">
          //           {item.children.map((child) => (
          //             <Link
          //               key={child.href}
          //               href={child.href}
          //               className={cn(
          //                 'flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors',
          //                 isActive(child.href)
          //                   ? 'bg-primary-100 text-primary-700'
          //                   : 'text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900'
          //               )}
          //             >
          //               <child.icon className="h-4 w-4 flex-shrink-0" />
          //               <span className="ml-3">{child.name}</span>
          //             </Link>
          //           ))}
          //         </div>
          //       )}
          //     </div>
          //   );
          // }

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                isActive(item.href)
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900'
              )}
            >
              <item.icon className="h-5 w-5 flex-shrink-0" />
              {!collapsed && <span className="ml-3">{item.name}</span>}
            </Link>
          );
        })}

        {/* Logout Button */}
        <div className="mt-auto pt-4 border-t border-neutral-200">
          <button
            onClick={handleLogout}
            className={cn(
              'flex w-full items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors',
              'text-red-600 hover:bg-red-50 hover:text-red-700'
            )}
          >
            <MdLogout className="h-5 w-5 flex-shrink-0" />
            {!collapsed && <span className="ml-3">Sign Out</span>}
          </button>
        </div>
      </nav>
    </div>
  );
};

export { Sidebar };
