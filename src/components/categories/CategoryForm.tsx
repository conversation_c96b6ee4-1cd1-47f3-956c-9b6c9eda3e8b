'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Select, SelectOption } from '@/components/ui/Select';
import { Label } from '@/components/ui/Label';
import { CategoryFormData, IconOption } from '@/types/category';
import { generateSlug, validateCategoryForm } from '@/lib/utils';
import { 
  Sun, 
  Battery, 
  Zap, 
  Cpu, 
  Package, 
  Settings, 
  Wrench, 
  Cable, 
  Power, 
  Lightbulb 
} from 'lucide-react';

interface CategoryFormProps {
  initialData?: Partial<CategoryFormData>;
  onSubmit: (data: CategoryFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

const iconMap = {
  sun: Sun,
  battery: Battery,
  zap: Zap,
  cpu: Cpu,
  package: Package,
  settings: Settings,
  wrench: Wrench,
  cable: Cable,
  power: Power,
  lightbulb: Lightbulb,
};

const iconOptions: SelectOption[] = [
  { value: 'sun', label: 'Sun' },
  { value: 'battery', label: 'Battery' },
  { value: 'zap', label: 'Lightning' },
  { value: 'cpu', label: 'CPU/Processor' },
  { value: 'package', label: 'Package' },
  { value: 'settings', label: 'Settings' },
  { value: 'wrench', label: 'Tool/Wrench' },
  { value: 'cable', label: 'Cable' },
  { value: 'power', label: 'Power' },
  { value: 'lightbulb', label: 'Light Bulb' },
];

const statusOptions: SelectOption[] = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

export const CategoryForm: React.FC<CategoryFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create',
}) => {
  const [formData, setFormData] = useState<CategoryFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    slug: initialData?.slug || '',
    icon: initialData?.icon || '',
    status: initialData?.status || 'active',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSlugManuallyEdited, setIsSlugManuallyEdited] = useState(false);

  // Auto-generate slug from name if not manually edited
  useEffect(() => {
    if (!isSlugManuallyEdited && formData.name) {
      setFormData(prev => ({
        ...prev,
        slug: generateSlug(formData.name),
      }));
    }
  }, [formData.name, isSlugManuallyEdited]);

  const handleInputChange = (field: keyof CategoryFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSlugChange = (value: string) => {
    setIsSlugManuallyEdited(true);
    handleInputChange('slug', value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateCategoryForm(formData);
    
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    onSubmit(formData);
  };

  const selectedIcon = formData.icon ? iconMap[formData.icon as keyof typeof iconMap] : null;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Name Field */}
        <div className="md:col-span-2">
          <Input
            label="Category Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter category name"
            error={errors.name}
            required
            fullWidth
          />
        </div>

        {/* Description Field */}
        <div className="md:col-span-2">
          <Textarea
            label="Description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter category description"
            error={errors.description}
            rows={4}
            required
            fullWidth
          />
        </div>

        {/* Slug Field */}
        <div>
          <Input
            label="Slug"
            value={formData.slug}
            onChange={(e) => handleSlugChange(e.target.value)}
            placeholder="category-slug"
            error={errors.slug}
            helperText="URL-friendly version of the name. Will be auto-generated if left empty."
            required
            fullWidth
          />
        </div>

        {/* Icon Field */}
        <div>
          <Label required>Icon</Label>
          <div className="mt-1 space-y-2">
            <Select
              options={iconOptions}
              value={formData.icon}
              onChange={(value) => handleInputChange('icon', value)}
              placeholder="Select an icon"
              error={errors.icon}
              fullWidth
            />
            {selectedIcon && (
              <div className="flex items-center space-x-2 p-2 bg-neutral-50 rounded-lg">
                <selectedIcon className="h-5 w-5 text-neutral-600" />
                <span className="text-sm text-neutral-600">Preview</span>
              </div>
            )}
          </div>
        </div>

        {/* Status Field */}
        <div className="md:col-span-2">
          <Select
            label="Status"
            options={statusOptions}
            value={formData.status}
            onChange={(value) => handleInputChange('status', value as 'active' | 'inactive')}
            fullWidth
          />
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-neutral-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={isLoading}
          disabled={isLoading}
        >
          {mode === 'create' ? 'Create Category' : 'Update Category'}
        </Button>
      </div>
    </form>
  );
};
