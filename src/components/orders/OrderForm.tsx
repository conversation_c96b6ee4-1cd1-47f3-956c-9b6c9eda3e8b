'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Select, SelectOption } from '@/components/ui/Select';
import { Label } from '@/components/ui/Label';
import { Card, CardContent } from '@/components/ui/Card';
import { OrderFormData, OrderItem } from '@/types/order';
import { Product } from '@/types/product';
import { formatCurrency } from '@/lib/utils';
import { Plus, Trash2, Package } from 'lucide-react';

interface OrderFormProps {
  initialData?: Partial<OrderFormData>;
  products: Product[];
  onSubmit: (data: OrderFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

const paymentMethodOptions: SelectOption[] = [
  { value: 'cash', label: 'Cash' },
  { value: 'transfer', label: 'Bank Transfer' },
  { value: 'card', label: 'Card Payment' },
  { value: 'whatsapp', label: 'WhatsApp Order' },
];

export const OrderForm: React.FC<OrderFormProps> = ({
  initialData,
  products,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create',
}) => {
  const [formData, setFormData] = useState<OrderFormData>({
    customerId: initialData?.customerId || 0,
    customerName: initialData?.customerName || '',
    customerEmail: initialData?.customerEmail || '',
    customerPhone: initialData?.customerPhone || '',
    customerAddress: initialData?.customerAddress || '',
    customerWhatsapp: initialData?.customerWhatsapp || '',
    items: initialData?.items || [{ productId: 0, quantity: 1, unitPrice: 0 }],
    shipping: initialData?.shipping || 0,
    discount: initialData?.discount || 0,
    tax: initialData?.tax || 0,
    paymentMethod: initialData?.paymentMethod || 'cash',
    notes: initialData?.notes || '',
    deliveryDate: initialData?.deliveryDate || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const productOptions: SelectOption[] = products.map(product => ({
    value: product.id.toString(),
    label: `${product.name} - ${formatCurrency(product.price)} (${product.stock} in stock)`,
  }));

  const handleInputChange = (field: keyof OrderFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleItemChange = (index: number, field: keyof OrderFormData['items'][0], value: any) => {
    const newItems = [...formData.items];
    newItems[index] = { ...newItems[index], [field]: value };

    // Auto-update unit price when product changes
    if (field === 'productId') {
      const product = products.find(p => p.id === parseInt(value));
      if (product) {
        newItems[index].unitPrice = product.price;
      }
    }

    setFormData(prev => ({
      ...prev,
      items: newItems,
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productId: 0, quantity: 1, unitPrice: 0 }],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  const calculateSubtotal = () => {
    return formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    return subtotal + formData.shipping + formData.tax - formData.discount;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Customer phone is required';
    }

    if (!formData.customerAddress.trim()) {
      newErrors.customerAddress = 'Customer address is required';
    }

    // Validate items
    formData.items.forEach((item, index) => {
      if (!item.productId || item.productId === 0) {
        newErrors[`item_${index}_product`] = 'Product is required';
      }
      if (item.quantity <= 0) {
        newErrors[`item_${index}_quantity`] = 'Quantity must be greater than 0';
      }
      if (item.unitPrice <= 0) {
        newErrors[`item_${index}_price`] = 'Price must be greater than 0';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Information */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-neutral-900 mb-4">Customer Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Customer Name"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              placeholder="Enter customer name"
              error={errors.customerName}
              required
              fullWidth
            />

            <Input
              label="Phone Number"
              value={formData.customerPhone}
              onChange={(e) => handleInputChange('customerPhone', e.target.value)}
              placeholder="Enter phone number"
              error={errors.customerPhone}
              required
              fullWidth
            />

            <Input
              label="Email Address"
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              placeholder="Enter email address"
              fullWidth
            />

            <Input
              label="WhatsApp Number"
              value={formData.customerWhatsapp}
              onChange={(e) => handleInputChange('customerWhatsapp', e.target.value)}
              placeholder="Enter WhatsApp number"
              fullWidth
            />

            <div className="md:col-span-2">
              <Textarea
                label="Address"
                value={formData.customerAddress}
                onChange={(e) => handleInputChange('customerAddress', e.target.value)}
                placeholder="Enter customer address"
                error={errors.customerAddress}
                rows={3}
                required
                fullWidth
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Items */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-neutral-900">Order Items</h3>
            <Button type="button" variant="outline" onClick={addItem}>
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>

          <div className="space-y-4">
            {formData.items.map((item, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border border-neutral-200 rounded-lg">
                <div className="md:col-span-5">
                  <Select
                    label="Product"
                    options={productOptions}
                    value={item.productId.toString()}
                    onChange={(value) => handleItemChange(index, 'productId', parseInt(value))}
                    placeholder="Select a product"
                    error={errors[`item_${index}_product`]}
                    required
                    fullWidth
                  />
                </div>

                <div className="md:col-span-2">
                  <Input
                    label="Quantity"
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 0)}
                    error={errors[`item_${index}_quantity`]}
                    required
                    fullWidth
                  />
                </div>

                <div className="md:col-span-2">
                  <Input
                    label="Unit Price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={item.unitPrice}
                    onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                    error={errors[`item_${index}_price`]}
                    required
                    fullWidth
                  />
                </div>

                <div className="md:col-span-2">
                  <Label>Total</Label>
                  <div className="h-11 flex items-center px-4 bg-neutral-50 border-2 border-neutral-200 rounded-lg">
                    <span className="font-medium">
                      {formatCurrency(item.quantity * item.unitPrice)}
                    </span>
                  </div>
                </div>

                <div className="md:col-span-1 flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeItem(index)}
                    disabled={formData.items.length === 1}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-neutral-900 mb-4">Order Summary</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Input
              label="Shipping Cost"
              type="number"
              step="0.01"
              min="0"
              value={formData.shipping}
              onChange={(e) => handleInputChange('shipping', parseFloat(e.target.value) || 0)}
              fullWidth
            />

            <Input
              label="Tax"
              type="number"
              step="0.01"
              min="0"
              value={formData.tax}
              onChange={(e) => handleInputChange('tax', parseFloat(e.target.value) || 0)}
              fullWidth
            />

            <Input
              label="Discount"
              type="number"
              step="0.01"
              min="0"
              value={formData.discount}
              onChange={(e) => handleInputChange('discount', parseFloat(e.target.value) || 0)}
              fullWidth
            />
          </div>

          <div className="space-y-2 mb-6">
            <div className="flex justify-between text-sm">
              <span>Subtotal:</span>
              <span>{formatCurrency(calculateSubtotal())}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Shipping:</span>
              <span>{formatCurrency(formData.shipping)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Tax:</span>
              <span>{formatCurrency(formData.tax)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Discount:</span>
              <span>-{formatCurrency(formData.discount)}</span>
            </div>
            <div className="flex justify-between text-lg font-bold border-t pt-2">
              <span>Total:</span>
              <span>{formatCurrency(calculateTotal())}</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Payment Method"
              options={paymentMethodOptions}
              value={formData.paymentMethod}
              onChange={(value) => handleInputChange('paymentMethod', value)}
              fullWidth
            />

            <Input
              label="Delivery Date"
              type="date"
              value={formData.deliveryDate}
              onChange={(e) => handleInputChange('deliveryDate', e.target.value)}
              fullWidth
            />
          </div>

          <div className="mt-4">
            <Textarea
              label="Notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Add any special instructions or notes"
              rows={3}
              fullWidth
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={isLoading}
          disabled={isLoading}
        >
          {mode === 'create' ? 'Create Order' : 'Update Order'}
        </Button>
      </div>
    </form>
  );
};
