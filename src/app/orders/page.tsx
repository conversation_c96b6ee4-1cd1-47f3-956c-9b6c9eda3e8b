'use client';

import React, { useState, useMemo } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useToast } from '@/components/ui/Toast';
import { Pagination } from '@/components/ui/Table';
import { OrderForm } from '@/components/orders/OrderForm';
import { OrderDetailModal } from '@/components/orders/OrderDetailModal';
import { Order, OrderFormData } from '@/types/order';
import { Product } from '@/types/product';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  Plus, 
  Search, 
  Eye,
  Edit,
  Trash2,
  Truck,
  Calendar,
  User,
  Package,
  MessageCircle,
  ShoppingCart,
  CreditCard,
  Clock
} from 'lucide-react';

// Mock products data
const mockProducts: Product[] = [
  {
    id: 1,
    name: 'Solar Panel 300W Monocrystalline',
    description: 'High-efficiency solar panel',
    category: 'Solar Panels',
    categoryId: 1,
    price: 299.99,
    costPrice: 180.00,
    stock: 45,
    minStock: 10,
    sku: 'SP-300W-MONO',
    barcode: '1234567890123',
    weight: 18.5,
    dimensions: { length: 165, width: 99, height: 4 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: true,
    warranty: '25 years',
    brand: 'SolarTech',
    model: 'ST-300M',
    createdAt: '2024-01-15',
    updatedAt: '2024-03-10',
  },
  {
    id: 2,
    name: 'Lithium Battery 100Ah LiFePO4',
    description: 'Long-lasting lithium battery',
    category: 'Batteries',
    categoryId: 2,
    price: 899.99,
    costPrice: 650.00,
    stock: 8,
    minStock: 10,
    sku: 'BAT-100AH-LIFEPO4',
    barcode: '2345678901234',
    weight: 12.8,
    dimensions: { length: 33, width: 17, height: 21 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: false,
    warranty: '10 years',
    brand: 'PowerCell',
    model: 'PC-100LFP',
    createdAt: '2024-01-20',
    updatedAt: '2024-03-08',
  },
];

// Mock orders data
const mockOrders: Order[] = [
  {
    id: 1,
    orderNumber: 'ORD-001',
    customer: {
      id: 1,
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Main St, Springfield, IL 62701',
      whatsappNumber: '+****************',
    },
    items: [
      {
        id: 1,
        productId: 1,
        productName: 'Solar Panel 300W Monocrystalline',
        productImage: '/api/placeholder/400/400',
        sku: 'SP-300W-MONO',
        quantity: 2,
        unitPrice: 299.99,
        totalPrice: 599.98,
      },
      {
        id: 2,
        productId: 2,
        productName: 'Lithium Battery 100Ah LiFePO4',
        sku: 'BAT-100AH-LIFEPO4',
        quantity: 1,
        unitPrice: 899.99,
        totalPrice: 899.99,
      },
    ],
    subtotal: 1499.97,
    tax: 150.00,
    shipping: 50.00,
    discount: 0,
    total: 1699.97,
    status: 'confirmed',
    paymentStatus: 'paid',
    paymentMethod: 'transfer',
    source: 'admin',
    notes: 'Customer requested express delivery',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T14:20:00Z',
    deliveryDate: '2024-01-20',
  },
  {
    id: 2,
    orderNumber: 'ORD-002',
    customer: {
      id: 2,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      address: '456 Oak Ave, Chicago, IL 60601',
      whatsappNumber: '+****************',
    },
    items: [
      {
        id: 3,
        productId: 1,
        productName: 'Solar Panel 300W Monocrystalline',
        productImage: '/api/placeholder/400/400',
        sku: 'SP-300W-MONO',
        quantity: 4,
        unitPrice: 299.99,
        totalPrice: 1199.96,
      },
    ],
    subtotal: 1199.96,
    tax: 120.00,
    shipping: 75.00,
    discount: 50.00,
    total: 1344.96,
    status: 'processing',
    paymentStatus: 'pending',
    paymentMethod: 'whatsapp',
    source: 'whatsapp',
    whatsappLink: 'https://wa.me/15552345678?text=Order%20Details%20ORD-002',
    notes: 'WhatsApp order - customer will pay on delivery',
    createdAt: '2024-01-14T09:15:00Z',
    updatedAt: '2024-01-14T16:45:00Z',
    deliveryDate: '2024-01-22',
  },
  {
    id: 3,
    orderNumber: 'ORD-003',
    customer: {
      id: 3,
      name: 'Mike Davis',
      email: '<EMAIL>',
      phone: '+****************',
      address: '789 Pine Rd, Austin, TX 73301',
    },
    items: [
      {
        id: 4,
        productId: 2,
        productName: 'Lithium Battery 100Ah LiFePO4',
        sku: 'BAT-100AH-LIFEPO4',
        quantity: 2,
        unitPrice: 899.99,
        totalPrice: 1799.98,
      },
    ],
    subtotal: 1799.98,
    tax: 180.00,
    shipping: 100.00,
    discount: 100.00,
    total: 1979.98,
    status: 'pending',
    paymentStatus: 'pending',
    paymentMethod: 'cash',
    source: 'admin',
    createdAt: '2024-01-13T14:20:00Z',
    updatedAt: '2024-01-13T14:20:00Z',
    deliveryDate: '2024-01-25',
  },
];

const getOrderStatusVariant = (status: Order['status']) => {
  switch (status) {
    case 'pending': return 'secondary';
    case 'confirmed': return 'default';
    case 'processing': return 'warning';
    case 'shipped': return 'default';
    case 'delivered': return 'success';
    case 'cancelled': return 'destructive';
    default: return 'secondary';
  }
};

const getPaymentStatusVariant = (status: Order['paymentStatus']) => {
  switch (status) {
    case 'pending': return 'warning';
    case 'paid': return 'success';
    case 'failed': return 'destructive';
    case 'refunded': return 'secondary';
    default: return 'secondary';
  }
};

const getSourceIcon = (source: Order['source']) => {
  switch (source) {
    case 'whatsapp': return MessageCircle;
    case 'website': return ShoppingCart;
    case 'admin': return User;
    default: return Package;
  }
};

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>(mockOrders);
  const [products] = useState<Product[]>(mockProducts);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [editingOrder, setEditingOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<Order | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [viewingOrder, setViewingOrder] = useState<Order | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const { addToast } = useToast();

  // Filter and paginate orders
  const filteredOrders = useMemo(() => {
    return orders.filter(order =>
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.phone.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [orders, searchTerm]);

  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedOrders = filteredOrders.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const openViewModal = (order: Order) => {
    setViewingOrder(order);
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setViewingOrder(null);
  };

  const openCreateModal = () => {
    setIsCreateModalOpen(true);
  };

  const closeCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const openEditModal = (order: Order) => {
    setEditingOrder(order);
    setIsEditModalOpen(true);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingOrder(null);
  };

  const handleCreateOrder = async (orderData: OrderFormData) => {
    setIsCreating(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // In a real app, this would create the order via API
      const newOrder: Order = {
        id: mockOrders.length + 1,
        orderNumber: `ORD-${String(mockOrders.length + 1).padStart(3, '0')}`,
        customer: {
          name: orderData.customerName,
          email: orderData.customerEmail,
          phone: orderData.customerPhone,
          address: orderData.customerAddress,
          whatsappNumber: orderData.customerWhatsapp,
        },
        items: orderData.items,
        subtotal: orderData.items.reduce((sum, item) => sum + item.totalPrice, 0),
        tax: 0,
        shipping: 0,
        discount: 0,
        total: orderData.items.reduce((sum, item) => sum + item.totalPrice, 0),
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: orderData.paymentMethod,
        source: 'admin',
        notes: orderData.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Add to mock data (in real app, this would be handled by the API)
      mockOrders.unshift(newOrder);

      addToast({
        type: 'success',
        title: 'Order Created',
        message: `Order ${newOrder.orderNumber} has been created successfully.`,
      });

      closeCreateModal();
    } catch (error) {
      console.error('Create order error:', error);
      addToast({
        type: 'error',
        title: 'Creation Failed',
        message: 'Failed to create order. Please try again.',
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <AdminLayout 
      title="Orders" 
      subtitle="Manage customer orders and WhatsApp requests"
    >
      <div className="space-y-8">
        {/* Order Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Orders</p>
                  <p className="text-2xl font-bold text-neutral-900">{orders.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <ShoppingCart className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(orders.reduce((sum, order) => sum + order.total, 0))}
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Pending Orders</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {orders.filter(order => order.status === 'pending').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-warning-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">WhatsApp Orders</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {orders.filter(order => order.source === 'whatsapp').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Input
              type="search"
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
              className="pl-10"
            />
          </div>
          <Button
            variant="default"
            className="whitespace-nowrap"
            onClick={openCreateModal}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Order
          </Button>
        </div>

        {/* Orders Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {paginatedOrders.map((order) => {
            const SourceIcon = getSourceIcon(order.source);

            return (
              <Card key={order.id} className="hover:shadow-strong transition-all duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Order Header */}
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold text-lg text-neutral-900">
                          {order.orderNumber}
                        </h3>
                        <p className="text-sm text-neutral-500">
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <SourceIcon className="h-4 w-4 text-neutral-500" />
                        <Badge
                          variant={getOrderStatusVariant(order.status)}
                          size="sm"
                        >
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                      </div>
                    </div>

                    {/* Customer Info */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-neutral-400" />
                        <span className="text-sm font-medium">{order.customer.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Package className="h-4 w-4 text-neutral-400" />
                        <span className="text-sm text-neutral-600">
                          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                      {order.deliveryDate && (
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-neutral-400" />
                          <span className="text-sm text-neutral-600">
                            Delivery: {formatDate(order.deliveryDate)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Order Total */}
                    <div className="flex items-center justify-between pt-2 border-t border-neutral-200">
                      <div>
                        <p className="text-2xl font-bold text-neutral-900">
                          {formatCurrency(order.total)}
                        </p>
                        <Badge
                          variant={getPaymentStatusVariant(order.paymentStatus)}
                          size="sm"
                        >
                          {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                        </Badge>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openViewModal(order)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditModal(order)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        {order.source === 'whatsapp' && order.whatsappLink && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(order.whatsappLink, '_blank')}
                          >
                            <MessageCircle className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Pagination */}
        {filteredOrders.length > 0 && (
          <Card>
            <CardContent className="p-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                totalItems={filteredOrders.length}
                onItemsPerPageChange={setItemsPerPage}
              />
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {filteredOrders.length === 0 && (
          <Card>
            <CardContent className="p-12">
              <div className="text-center">
                <ShoppingCart className="h-16 w-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-neutral-900 mb-2">
                  {searchTerm ? 'No orders found' : 'No orders yet'}
                </h3>
                <p className="text-neutral-600 mb-6">
                  {searchTerm
                    ? 'Try adjusting your search terms.'
                    : 'Orders from customers will appear here.'
                  }
                </p>
                {!searchTerm && (
                  <Button onClick={openCreateModal}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Order
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Order Detail Modal */}
      <OrderDetailModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        order={viewingOrder}
      />

      {/* Create Order Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        title="Create New Order"
        description="Create a new order for a customer"
        size="xl"
      >
        <OrderForm
          products={mockProducts}
          onSubmit={handleCreateOrder}
          onCancel={closeCreateModal}
          isLoading={isCreating}
          mode="create"
        />
      </Modal>

      {/* Edit Order Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        title="Edit Order"
        description="Update order information"
        size="xl"
      >
        {editingOrder && (
          <OrderForm
            initialData={{
              customerName: editingOrder.customer.name,
              customerEmail: editingOrder.customer.email,
              customerPhone: editingOrder.customer.phone,
              customerAddress: editingOrder.customer.address,
              customerWhatsapp: editingOrder.customer.whatsappNumber || '',
              paymentMethod: editingOrder.paymentMethod,
              notes: editingOrder.notes || '',
              items: editingOrder.items,
            }}
            products={mockProducts}
            onSubmit={handleCreateOrder}
            onCancel={closeEditModal}
            isLoading={isCreating}
            mode="edit"
          />
        )}
      </Modal>
    </AdminLayout>
  );
}
