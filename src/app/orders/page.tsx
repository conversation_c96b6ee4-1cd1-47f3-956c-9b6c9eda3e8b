import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Truck,
  Calendar,
  User,
  Package
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// Mock data for orders
const orders = [
  {
    id: 'ORD-001',
    customer: '<PERSON>',
    email: '<EMAIL>',
    date: '2024-01-15',
    status: 'completed',
    total: 2450.00,
    items: 3,
    shippingAddress: '123 Main St, City, State 12345',
  },
  {
    id: 'ORD-002',
    customer: '<PERSON>',
    email: '<EMAIL>',
    date: '2024-01-14',
    status: 'processing',
    total: 1890.00,
    items: 2,
    shippingAddress: '456 Oak Ave, City, State 67890',
  },
  {
    id: 'ORD-003',
    customer: '<PERSON>',
    email: '<EMAIL>',
    date: '2024-01-13',
    status: 'pending',
    total: 3200.00,
    items: 5,
    shippingAddress: '789 Pine Rd, City, State 54321',
  },
  {
    id: 'ORD-004',
    customer: 'Emily Wilson',
    email: '<EMAIL>',
    date: '2024-01-12',
    status: 'shipped',
    total: 899.99,
    items: 1,
    shippingAddress: '321 Elm St, City, State 98765',
  },
];

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'processing':
      return 'warning';
    case 'shipped':
      return 'secondary';
    case 'pending':
      return 'secondary';
    default:
      return 'secondary';
  }
};

export default function OrdersPage() {
  return (
    <AdminLayout 
      title="Orders" 
      subtitle="Manage customer orders and fulfillment"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Input
                type="search"
                placeholder="Search orders..."
                leftIcon={<Search className="h-4 w-4" />}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="whitespace-nowrap">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            New Order
          </Button>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {orders.map((order) => (
            <Card key={order.id} className="hover:shadow-strong transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  {/* Order Info */}
                  <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                      <h3 className="font-semibold text-lg text-neutral-900">{order.id}</h3>
                      <Badge variant={getStatusVariant(order.status)} size="sm">
                        {order.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">{order.customer}</p>
                          <p className="text-neutral-600">{order.email}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">Order Date</p>
                          <p className="text-neutral-600">{order.date}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">{order.items} Items</p>
                          <p className="text-neutral-600">Total: {formatCurrency(order.total)}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Truck className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">Shipping</p>
                          <p className="text-neutral-600 line-clamp-1">{order.shippingAddress}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    {order.status === 'processing' && (
                      <Button variant="default" size="sm">
                        <Truck className="h-4 w-4 mr-2" />
                        Ship
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Order Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Orders</p>
                  <p className="text-2xl font-bold text-neutral-900">{orders.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Pending</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {orders.filter(o => o.status === 'pending').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-warning-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Processing</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {orders.filter(o => o.status === 'processing').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <Truck className="h-6 w-6 text-secondary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Revenue</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(orders.reduce((sum, order) => sum + order.total, 0))}
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
