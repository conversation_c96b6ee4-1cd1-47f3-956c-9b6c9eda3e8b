import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

const customers = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main St, Springfield, IL 62701',
    joinDate: '2024-01-15',
    totalOrders: 5,
    totalSpent: 12450.00,
    status: 'active',
    lastOrder: '2024-03-10',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Chicago, IL 60601',
    joinDate: '2024-02-20',
    totalOrders: 3,
    totalSpent: 8900.00,
    status: 'active',
    lastOrder: '2024-03-08',
  },
  {
    id: 3,
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine Rd, Austin, <PERSON> 73301',
    join<PERSON>ate: '2023-11-10',
    totalOrders: 8,
    total<PERSON>pent: 25600.00,
    status: 'vip',
    lastOrder: '2024-03-12',
  },
  {
    id: 4,
    name: 'Emily <PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Elm St, Denver, CO 80201',
    joinDate: '2024-03-01',
    totalOrders: 1,
    totalSpent: 899.99,
    status: 'new',
    lastOrder: '2024-03-01',
  },
];

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'vip':
      return 'default';
    case 'active':
      return 'success';
    case 'new':
      return 'secondary';
    case 'inactive':
      return 'warning';
    default:
      return 'secondary';
  }
};

export default function CustomersPage() {
  return (
    <AdminLayout
      title="Customers"
      subtitle="Manage your customer relationships"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Input
                type="search"
                placeholder="Search customers..."
                leftIcon={<Search className="h-4 w-4" />}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="whitespace-nowrap">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>

        {/* Customers List */}
        <div className="space-y-4">
          {customers.map((customer) => (
            <Card key={customer.id} className="hover:shadow-strong transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  {/* Customer Info */}
                  <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                      <h3 className="font-semibold text-lg text-neutral-900">{customer.name}</h3>
                      <Badge variant={getStatusVariant(customer.status)} size="sm">
                        {customer.status.toUpperCase()}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">Email</p>
                          <p className="text-neutral-600">{customer.email}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">Phone</p>
                          <p className="text-neutral-600">{customer.phone}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">Address</p>
                          <p className="text-neutral-600 line-clamp-1">{customer.address}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-neutral-400" />
                        <div>
                          <p className="font-medium text-neutral-900">{customer.totalOrders} Orders</p>
                          <p className="text-neutral-600">{formatCurrency(customer.totalSpent)}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Mail className="h-4 w-4 mr-2" />
                      Contact
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Customer Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Customers</p>
                  <p className="text-2xl font-bold text-neutral-900">{customers.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">VIP Customers</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {customers.filter(c => c.status === 'vip').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <User className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">New This Month</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {customers.filter(c => c.status === 'new').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(customers.reduce((sum, customer) => sum + customer.totalSpent, 0))}
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
