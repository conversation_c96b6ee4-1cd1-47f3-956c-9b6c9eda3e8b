import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  MdAdd,
  <PERSON>dSearch,
  MdFilterList,
  MdEdit,
  MdDelete,
  MdVisibility,
  MdInventory,
  MdWarning
} from 'react-icons/md';
import { formatCurrency, getStockStatus } from '@/lib/utils';

// Mock data for products
const products = [
  {
    id: 1,
    name: 'Solar Panel 300W Monocrystalline',
    category: 'Solar Panels',
    price: 299.99,
    stock: 45,
    minStock: 10,
    image: '/api/placeholder/100/100',
    status: 'active',
    sku: 'SP-300W-MONO',
  },
  {
    id: 2,
    name: 'Lithium Battery 100Ah LiFePO4',
    category: 'Batteries',
    price: 899.99,
    stock: 8,
    minStock: 10,
    image: '/api/placeholder/100/100',
    status: 'active',
    sku: 'BAT-100AH-LIFEPO4',
  },
  {
    id: 3,
    name: 'MPPT Solar Charge Controller 40A',
    category: 'Controllers',
    price: 159.99,
    stock: 23,
    minStock: 5,
    image: '/api/placeholder/100/100',
    status: 'active',
    sku: 'CTRL-MPPT-40A',
  },
  {
    id: 4,
    name: 'Pure Sine Wave Inverter 2000W',
    category: 'Inverters',
    price: 449.99,
    stock: 12,
    minStock: 8,
    image: '/api/placeholder/100/100',
    status: 'active',
    sku: 'INV-PSW-2000W',
  },
];

export default function ProductsPage() {
  return (
    <AdminLayout 
      title="Products" 
      subtitle="Manage your solar equipment inventory"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Input
                type="search"
                placeholder="Search products..."
                leftIcon={<MdSearch className="h-4 w-4" />}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="whitespace-nowrap">
              <MdFilterList className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <MdAdd className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => {
            const stockStatus = getStockStatus(product.stock, product.minStock);
            
            return (
              <Card key={product.id} className="group hover:shadow-strong transition-all duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Product Image */}
                    <div className="aspect-square bg-gradient-to-br from-neutral-50 to-neutral-100 rounded-xl flex items-center justify-center border-2 border-dashed border-neutral-200">
                      <MdInventory className="h-12 w-12 text-neutral-400" />
                    </div>
                    
                    {/* Product Info */}
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold text-neutral-900 line-clamp-2 text-sm">
                          {product.name}
                        </h3>
                        <Badge variant="secondary" size="sm">
                          {product.category}
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-neutral-600">SKU: {product.sku}</p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-neutral-900">
                          {formatCurrency(product.price)}
                        </span>
                        <Badge 
                          variant={stockStatus.status === 'in-stock' ? 'success' : 'warning'}
                          size="sm"
                        >
                          {product.stock} in stock
                        </Badge>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <MdVisibility className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <MdEdit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <MdDelete className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Low Stock Alert */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-warning-700">
              <MdWarning className="h-5 w-5 mr-2" />
              Low Stock Alert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {products
                .filter(product => product.stock <= product.minStock)
                .map(product => (
                  <div key={product.id} className="flex items-center justify-between p-3 bg-warning-50 rounded-lg border border-warning-200">
                    <div>
                      <p className="font-medium text-warning-900">{product.name}</p>
                      <p className="text-sm text-warning-700">Only {product.stock} left in stock</p>
                    </div>
                    <Button variant="warning" size="sm">
                      Restock
                    </Button>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
