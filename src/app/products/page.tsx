'use client';

import React, { useState, useMemo } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useToast } from '@/components/ui/Toast';
import { Pagination } from '@/components/ui/Table';
import { ProductForm } from '@/components/products/ProductForm';
import { Product, ProductFormData } from '@/types/product';
import { Category } from '@/types/category';
import { formatCurrency, getStockStatus } from '@/lib/utils';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  TrendingUp,
  AlertTriangle,
  Eye
} from 'lucide-react';

// Mock categories data
const mockCategories: Category[] = [
  { id: 1, name: 'Solar Panels', description: 'Photovoltaic panels', slug: 'solar-panels', icon: 'sun', productCount: 24, status: 'active' },
  { id: 2, name: 'Batteries', description: 'Energy storage solutions', slug: 'batteries', icon: 'battery', productCount: 18, status: 'active' },
  { id: 3, name: 'Inverters', description: 'DC to AC conversion', slug: 'inverters', icon: 'zap', productCount: 12, status: 'active' },
  { id: 4, name: 'Controllers', description: 'Charge controllers', slug: 'controllers', icon: 'cpu', productCount: 15, status: 'active' },
  { id: 5, name: 'Accessories', description: 'Cables and hardware', slug: 'accessories', icon: 'package', productCount: 32, status: 'active' },
];

// Mock products data with enhanced structure
const mockProducts: Product[] = [
  {
    id: 1,
    name: 'Solar Panel 300W Monocrystalline',
    description: 'High-efficiency monocrystalline solar panel with 300W power output',
    category: 'Solar Panels',
    categoryId: 1,
    price: 299.99,
    costPrice: 180.00,
    stock: 45,
    minStock: 10,
    sku: 'SP-300W-MONO',
    barcode: '1234567890123',
    weight: 18.5,
    dimensions: { length: 165, width: 99, height: 4 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: true,
    warranty: '25 years',
    brand: 'SolarTech',
    model: 'ST-300M',
    createdAt: '2024-01-15',
    updatedAt: '2024-03-10',
  },
  {
    id: 2,
    name: 'Lithium Battery 100Ah LiFePO4',
    description: 'Long-lasting lithium iron phosphate battery with 100Ah capacity',
    category: 'Batteries',
    categoryId: 2,
    price: 899.99,
    costPrice: 650.00,
    stock: 8,
    minStock: 10,
    sku: 'BAT-100AH-LIFEPO4',
    barcode: '2345678901234',
    weight: 12.8,
    dimensions: { length: 33, width: 17, height: 21 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: false,
    warranty: '10 years',
    brand: 'PowerCell',
    model: 'PC-100LFP',
    createdAt: '2024-01-20',
    updatedAt: '2024-03-08',
  },
  {
    id: 3,
    name: 'MPPT Solar Charge Controller 40A',
    description: 'Maximum Power Point Tracking charge controller with 40A capacity',
    category: 'Controllers',
    categoryId: 4,
    price: 159.99,
    costPrice: 95.00,
    stock: 23,
    minStock: 5,
    sku: 'CTRL-MPPT-40A',
    barcode: '3456789012345',
    weight: 1.2,
    dimensions: { length: 19, width: 13, height: 6 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: true,
    warranty: '5 years',
    brand: 'ChargeMax',
    model: 'CM-40MPPT',
    createdAt: '2024-02-01',
    updatedAt: '2024-03-05',
  },
  {
    id: 4,
    name: 'Pure Sine Wave Inverter 2000W',
    description: 'High-quality pure sine wave inverter with 2000W continuous power',
    category: 'Inverters',
    categoryId: 3,
    price: 449.99,
    costPrice: 280.00,
    stock: 12,
    minStock: 8,
    sku: 'INV-PSW-2000W',
    barcode: '4567890123456',
    weight: 8.5,
    dimensions: { length: 35, width: 18, height: 12 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: false,
    warranty: '3 years',
    brand: 'PowerWave',
    model: 'PW-2000PSW',
    createdAt: '2024-02-10',
    updatedAt: '2024-03-12',
  },
  {
    id: 5,
    name: 'Solar Cable 4mm² Red',
    description: 'High-quality solar cable suitable for outdoor installations',
    category: 'Accessories',
    categoryId: 5,
    price: 2.99,
    costPrice: 1.50,
    stock: 0,
    minStock: 50,
    sku: 'CABLE-4MM-RED',
    barcode: '5678901234567',
    weight: 0.08,
    dimensions: { length: 100, width: 0.8, height: 0.8 },
    images: ['/api/placeholder/400/400'],
    status: 'active',
    featured: false,
    warranty: '10 years',
    brand: 'CableTech',
    model: 'CT-4RED',
    createdAt: '2024-02-15',
    updatedAt: '2024-03-15',
  },
];

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [categories] = useState<Category[]>(mockCategories);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { addToast } = useToast();

  // Filter and paginate products
  const filteredProducts = useMemo(() => {
    return products.filter(product =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [products, searchTerm]);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleCreateProduct = async (data: ProductFormData) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const category = categories.find(c => c.id === data.categoryId);
      const newProduct: Product = {
        id: Math.max(...products.map(p => p.id)) + 1,
        ...data,
        category: category?.name || '',
        images: ['/api/placeholder/400/400'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setProducts(prev => [...prev, newProduct]);
      setIsFormModalOpen(false);

      addToast({
        type: 'success',
        title: 'Product Created',
        message: `${data.name} has been created successfully.`,
      });
    } catch (error) {
      console.error('Error creating product:', error);
      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to create product. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateProduct = async (data: ProductFormData) => {
    if (!editingProduct) return;

    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const category = categories.find(c => c.id === data.categoryId);
      setProducts(prev => prev.map(product =>
        product.id === editingProduct.id
          ? { ...product, ...data, category: category?.name || '', updatedAt: new Date().toISOString() }
          : product
      ));

      setIsFormModalOpen(false);
      setEditingProduct(null);

      addToast({
        type: 'success',
        title: 'Product Updated',
        message: `${data.name} has been updated successfully.`,
      });
    } catch (error) {
      console.error('Error updating product:', error);
      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to update product. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProduct = async () => {
    if (!productToDelete) return;

    setIsDeleting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setProducts(prev => prev.filter(product => product.id !== productToDelete.id));
      setIsDeleteModalOpen(false);
      setProductToDelete(null);

      addToast({
        type: 'success',
        title: 'Product Deleted',
        message: `${productToDelete.name} has been deleted successfully.`,
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete product. Please try again.',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const openCreateModal = () => {
    setEditingProduct(null);
    setIsFormModalOpen(true);
  };

  const openEditModal = (product: Product) => {
    setEditingProduct(product);
    setIsFormModalOpen(true);
  };

  const closeModal = () => {
    setIsFormModalOpen(false);
    setEditingProduct(null);
  };

  const openDeleteModal = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setProductToDelete(null);
  };

  return (
    <AdminLayout
      title="Products"
      subtitle="Manage your solar equipment inventory"
    >
      <div className="space-y-8">
        {/* Product Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Products</p>
                  <p className="text-2xl font-bold text-neutral-900">{products.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Value</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(products.reduce((sum, p) => sum + (p.price * p.stock), 0))}
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Low Stock</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {products.filter(p => p.stock <= p.minStock).length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-warning-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Out of Stock</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {products.filter(p => p.stock === 0).length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-error-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-error-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Input
              type="search"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
              className="pl-10"
            />
          </div>
          <Button variant="default" className="whitespace-nowrap" onClick={openCreateModal}>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {paginatedProducts.map((product) => {
            const stockStatus = getStockStatus(product.stock, product.minStock);

            return (
              <Card key={product.id} className="group hover:shadow-strong transition-all duration-200">
                <CardContent className="p-0">
                  {/* Product Image */}
                  <div className="aspect-square bg-gradient-to-br from-neutral-50 to-neutral-100 rounded-t-lg overflow-hidden">
                    {product.images && product.images.length > 0 ? (
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="h-16 w-16 text-neutral-400" />
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="p-4 space-y-3">
                    <div className="space-y-1">
                      <h3 className="font-semibold text-neutral-900 text-lg line-clamp-2">
                        {product.name}
                      </h3>
                      <p className="text-sm text-neutral-500">
                        {product.category} • {product.sku}
                      </p>
                      {product.brand && product.model && (
                        <p className="text-xs text-neutral-400">
                          {product.brand} {product.model}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <p className="text-2xl font-bold text-neutral-900">
                          {formatCurrency(product.price)}
                        </p>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={stockStatus.variant}
                            size="sm"
                          >
                            {stockStatus.label}
                          </Badge>
                          <span className="text-sm text-neutral-500">
                            {product.stock} in stock
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={product.status === 'active' ? 'default' : product.status === 'draft' ? 'secondary' : 'secondary'}
                          size="sm"
                        >
                          {product.status === 'active' ? 'Active' : product.status === 'draft' ? 'Draft' : 'Inactive'}
                        </Badge>
                        {product.featured && (
                          <div className="mt-1">
                            <Badge variant="warning" size="sm">
                              Featured
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => openEditModal(product)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openDeleteModal(product)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Pagination */}
        {filteredProducts.length > 0 && (
          <Card>
            <CardContent className="p-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                totalItems={filteredProducts.length}
                onItemsPerPageChange={setItemsPerPage}
              />
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {filteredProducts.length === 0 && (
          <Card>
            <CardContent className="p-12">
              <div className="text-center">
                <Package className="h-16 w-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-neutral-900 mb-2">
                  {searchTerm ? 'No products found' : 'No products yet'}
                </h3>
                <p className="text-neutral-600 mb-6">
                  {searchTerm
                    ? 'Try adjusting your search terms.'
                    : 'Get started by creating your first product.'
                  }
                </p>
                {!searchTerm && (
                  <Button onClick={openCreateModal}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Product
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

      </div>

      {/* Product Form Modal */}
      <Modal
        isOpen={isFormModalOpen}
        onClose={closeModal}
        title={editingProduct ? 'Edit Product' : 'Create New Product'}
        description={editingProduct ? 'Update the product details below.' : 'Fill in the details to create a new product.'}
        size="xl"
      >
        <ProductForm
          initialData={editingProduct || undefined}
          categories={categories}
          onSubmit={editingProduct ? handleUpdateProduct : handleCreateProduct}
          onCancel={closeModal}
          isLoading={isLoading}
          mode={editingProduct ? 'edit' : 'create'}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDeleteProduct}
        title="Delete Product"
        message={`Are you sure you want to delete "${productToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        isLoading={isDeleting}
      />
    </AdminLayout>
  );
}
