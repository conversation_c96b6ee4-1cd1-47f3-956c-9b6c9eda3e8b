/* Poppins font is loaded via HTML link tag for better performance */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary colors - Solar Blue */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --primary-50: 214 100% 97%;
    --primary-100: 214 95% 93%;
    --primary-200: 213 97% 87%;
    --primary-300: 212 96% 78%;
    --primary-400: 213 94% 68%;
    --primary-500: 217 91% 60%;
    --primary-600: 221 83% 53%;
    --primary-700: 224 76% 48%;
    --primary-800: 226 71% 40%;
    --primary-900: 224 64% 33%;
    --primary-950: 226 55% 21%;

    /* Secondary colors - Solar Orange */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-50: 33 100% 96%;
    --secondary-100: 34 100% 92%;
    --secondary-200: 32 98% 83%;
    --secondary-300: 31 97% 72%;
    --secondary-400: 27 96% 61%;
    --secondary-500: 25 95% 53%;
    --secondary-600: 21 90% 48%;
    --secondary-700: 17 88% 40%;
    --secondary-800: 15 79% 34%;
    --secondary-900: 15 75% 28%;
    --secondary-950: 13 81% 14%;

    /* Semantic colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Success colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --success-50: 138 76% 97%;
    --success-100: 141 84% 93%;
    --success-200: 141 79% 85%;
    --success-300: 142 77% 73%;
    --success-400: 142 69% 58%;
    --success-500: 142 71% 45%;
    --success-600: 142 76% 36%;
    --success-700: 142 72% 29%;
    --success-800: 143 64% 24%;
    --success-900: 144 61% 20%;
    --success-950: 145 80% 10%;

    /* Warning colors */
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --warning-50: 48 100% 96%;
    --warning-100: 48 96% 89%;
    --warning-200: 48 97% 77%;
    --warning-300: 46 97% 65%;
    --warning-400: 43 96% 56%;
    --warning-500: 38 92% 50%;
    --warning-600: 32 95% 44%;
    --warning-700: 26 90% 37%;
    --warning-800: 23 83% 31%;
    --warning-900: 22 78% 26%;
    --warning-950: 15 86% 14%;

    /* Error colors */
    --error: 0 84% 60%;
    --error-foreground: 210 40% 98%;
    --error-50: 0 86% 97%;
    --error-100: 0 93% 94%;
    --error-200: 0 96% 89%;
    --error-300: 0 94% 82%;
    --error-400: 0 91% 71%;
    --error-500: 0 84% 60%;
    --error-600: 0 72% 51%;
    --error-700: 0 74% 42%;
    --error-800: 0 70% 35%;
    --error-900: 0 63% 31%;
    --error-950: 0 75% 15%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Improved focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Better text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }

  h5 {
    @apply text-lg lg:text-xl;
  }

  h6 {
    @apply text-base lg:text-lg;
  }

  p {
    @apply leading-7;
  }

  /* Link styles */
  a {
    @apply text-primary hover:text-primary/80 transition-colors;
  }
}

/* Modern scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted/30;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

::-webkit-scrollbar-corner {
  @apply bg-transparent;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

@layer components {
  /* Utility classes */
  .glass {
    @apply bg-background/80 backdrop-blur-md border border-border/50;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-in {
    @apply animate-fade-in;
  }

  .animate-out {
    @apply animate-fade-out;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Interactive states */
  .interactive {
    @apply transition-all duration-200 ease-in-out;
  }

  .interactive:hover {
    @apply scale-[1.02] shadow-lg;
  }

  .interactive:active {
    @apply scale-[0.98];
  }

  /* Card variants */
  .card-elevated {
    @apply bg-card border border-border shadow-soft rounded-lg;
  }

  .card-glass {
    @apply glass shadow-medium rounded-lg;
  }

  /* Focus ring utility */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* Poppins font optimizations */
  body {
    font-family: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Enhanced typography for headings */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', system-ui, sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  /* Button and interactive elements */
  button, .btn {
    font-family: 'Poppins', system-ui, sans-serif;
    font-weight: 500;
  }

  /* Form elements */
  input, textarea, select {
    font-family: 'Poppins', system-ui, sans-serif;
  }
}
