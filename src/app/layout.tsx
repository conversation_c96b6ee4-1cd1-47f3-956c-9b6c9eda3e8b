import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-geist-sans",
  display: "swap",
});

const interMono = Inter({
  subsets: ["latin"],
  variable: "--font-geist-mono",
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "Ecopulse Admin",
    template: "%s | Ecopulse Admin",
  },
  description: "Professional admin panel for solar equipment management with modern design and powerful features",
  keywords: ["solar", "admin", "panel", "management", "dashboard", "equipment", "ecopulse"],
  authors: [{ name: "Ecopulse Team" }],
  creator: "Ecopulse Team",
  metadataBase: new URL("https://ecopulse.com"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ecopulse.com",
    title: "Ecopulse Admin",
    description: "Professional admin panel for solar equipment management",
    siteName: "Ecopulse Admin",
  },
  twitter: {
    card: "summary_large_image",
    title: "Ecopulse Admin",
    description: "Professional admin panel for solar equipment management",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${interMono.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
