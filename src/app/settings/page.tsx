'use client';

import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import {
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  Database,
  Mail,
  Phone,
  MapPin,
  Save,
  Eye,
  EyeOff
} from 'lucide-react';
import { useState } from 'react';

export default function SettingsPage() {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <AdminLayout
      title="Settings"
      subtitle="Configure your Ecopulse admin panel preferences"
    >
      <div className="space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <nav className="space-y-2">
                  <a href="#profile" className="flex items-center space-x-3 px-3 py-2 rounded-lg bg-primary-50 text-primary-700 font-medium">
                    <User className="h-4 w-4" />
                    <span>Profile</span>
                  </a>
                  <a href="#notifications" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-neutral-600 hover:bg-neutral-50">
                    <Bell className="h-4 w-4" />
                    <span>Notifications</span>
                  </a>
                  <a href="#security" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-neutral-600 hover:bg-neutral-50">
                    <Shield className="h-4 w-4" />
                    <span>Security</span>
                  </a>
                  <a href="#appearance" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-neutral-600 hover:bg-neutral-50">
                    <Palette className="h-4 w-4" />
                    <span>Appearance</span>
                  </a>
                  <a href="#integrations" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-neutral-600 hover:bg-neutral-50">
                    <Globe className="h-4 w-4" />
                    <span>Integrations</span>
                  </a>
                  <a href="#data" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-neutral-600 hover:bg-neutral-50">
                    <Database className="h-4 w-4" />
                    <span>Data & Privacy</span>
                  </a>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Profile Settings */}
            <Card id="profile">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-6">
                  <div className="h-20 w-20 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <User className="h-10 w-10 text-white" />
                  </div>
                  <div>
                    <Button variant="outline" size="sm">
                      Change Avatar
                    </Button>
                    <p className="text-sm text-neutral-600 mt-1">JPG, GIF or PNG. 1MB max.</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      First Name
                    </label>
                    <Input defaultValue="John" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Last Name
                    </label>
                    <Input defaultValue="Doe" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Email Address
                    </label>
                    <Input
                      type="email"
                      defaultValue="<EMAIL>"
                      leftIcon={<Mail className="h-4 w-4" />}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Phone Number
                    </label>
                    <Input
                      defaultValue="+****************"
                      leftIcon={<Phone className="h-4 w-4" />}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Address
                  </label>
                  <Input
                    defaultValue="123 Business Ave, Suite 100, City, State 12345"
                    leftIcon={<MapPin className="h-4 w-4" />}
                  />
                </div>

                <div className="flex justify-end">
                  <Button variant="default">
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card id="security">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Current Password
                  </label>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter current password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      New Password
                    </label>
                    <Input type="password" placeholder="Enter new password" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Confirm Password
                    </label>
                    <Input type="password" placeholder="Confirm new password" />
                  </div>
                </div>

                <div className="border-t border-neutral-200 pt-6">
                  <h4 className="font-medium text-neutral-900 mb-4">Two-Factor Authentication</h4>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-neutral-900">Enable 2FA</p>
                      <p className="text-sm text-neutral-600">Add an extra layer of security to your account</p>
                    </div>
                    <Badge variant="warning">Disabled</Badge>
                  </div>
                  <Button variant="outline" size="sm" className="mt-4">
                    Enable 2FA
                  </Button>
                </div>

                <div className="flex justify-end">
                  <Button variant="default">
                    <Save className="h-4 w-4 mr-2" />
                    Update Password
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Notification Settings */}
            <Card id="notifications">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  Notification Preferences
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-neutral-900">Email Notifications</p>
                      <p className="text-sm text-neutral-600">Receive notifications via email</p>
                    </div>
                    <Badge variant="success">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-neutral-900">Low Stock Alerts</p>
                      <p className="text-sm text-neutral-600">Get notified when products are running low</p>
                    </div>
                    <Badge variant="success">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-neutral-900">New Order Alerts</p>
                      <p className="text-sm text-neutral-600">Get notified about new customer orders</p>
                    </div>
                    <Badge variant="success">Enabled</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-neutral-900">Marketing Updates</p>
                      <p className="text-sm text-neutral-600">Receive updates about new features and promotions</p>
                    </div>
                    <Badge variant="secondary">Disabled</Badge>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button variant="default">
                    <Save className="h-4 w-4 mr-2" />
                    Save Preferences
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
