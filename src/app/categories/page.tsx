import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { 
  Plus, 
  Search, 
  Edit,
  Trash2,
  Sun,
  Battery,
  Zap,
  Cpu,
  Package
} from 'lucide-react';

// Mock data for categories
const categories = [
  {
    id: 1,
    name: 'Solar Panels',
    description: 'Photovoltaic panels for solar energy generation',
    icon: Sun,
    productCount: 24,
    status: 'active',
    color: 'bg-yellow-100 text-yellow-700',
  },
  {
    id: 2,
    name: 'Batteries',
    description: 'Energy storage solutions for solar systems',
    icon: Battery,
    productCount: 18,
    status: 'active',
    color: 'bg-green-100 text-green-700',
  },
  {
    id: 3,
    name: 'Inverters',
    description: 'DC to AC power conversion equipment',
    icon: Zap,
    productCount: 12,
    status: 'active',
    color: 'bg-blue-100 text-blue-700',
  },
  {
    id: 4,
    name: 'Controllers',
    description: 'Charge controllers and system management',
    icon: Cpu,
    productCount: 15,
    status: 'active',
    color: 'bg-purple-100 text-purple-700',
  },
  {
    id: 5,
    name: 'Accessories',
    description: 'Cables, connectors, and mounting hardware',
    icon: Package,
    productCount: 32,
    status: 'active',
    color: 'bg-gray-100 text-gray-700',
  },
];

export default function CategoriesPage() {
  return (
    <AdminLayout 
      title="Categories" 
      subtitle="Organize your product catalog"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Input
              type="search"
              placeholder="Search categories..."
              leftIcon={<Search className="h-4 w-4" />}
              className="pl-10"
            />
          </div>
          <Button variant="default" className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => {
            const IconComponent = category.icon;
            
            return (
              <Card key={category.id} className="group hover:shadow-strong transition-all duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Category Icon */}
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${category.color}`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    
                    {/* Category Info */}
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold text-neutral-900 text-lg">
                          {category.name}
                        </h3>
                        <Badge variant="success" size="sm">
                          Active
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-neutral-600 line-clamp-2">
                        {category.description}
                      </p>
                      
                      <div className="flex items-center justify-between pt-2">
                        <span className="text-sm text-neutral-500">
                          {category.productCount} products
                        </span>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Category Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Categories</p>
                  <p className="text-2xl font-bold text-neutral-900">{categories.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Products</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {categories.reduce((sum, cat) => sum + cat.productCount, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-secondary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Active Categories</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {categories.filter(cat => cat.status === 'active').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
