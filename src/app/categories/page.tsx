'use client';

import React, { useState, useMemo } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  Pagination,
} from '@/components/ui/Table';
import { CategoryForm } from '@/components/categories/CategoryForm';
import { Category, CategoryFormData } from '@/types/category';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Sun,
  Battery,
  Zap,
  Cpu,
  Package,
  Settings,
  Wrench,
  Cable,
  Power,
  Lightbulb
} from 'lucide-react';

// Icon mapping
const iconMap = {
  sun: Sun,
  battery: Battery,
  zap: Zap,
  cpu: Cpu,
  package: Package,
  settings: Settings,
  wrench: Wrench,
  cable: Cable,
  power: Power,
  lightbulb: Lightbulb,
};

// Mock data for categories with updated structure
const mockCategories: Category[] = [
  {
    id: 1,
    name: 'Solar Panels',
    description: 'Photovoltaic panels for solar energy generation',
    slug: 'solar-panels',
    icon: 'sun',
    productCount: 24,
    status: 'active',
    createdAt: '2024-01-15',
    updatedAt: '2024-03-10',
  },
  {
    id: 2,
    name: 'Batteries',
    description: 'Energy storage solutions for solar systems',
    slug: 'batteries',
    icon: 'battery',
    productCount: 18,
    status: 'active',
    createdAt: '2024-01-20',
    updatedAt: '2024-03-08',
  },
  {
    id: 3,
    name: 'Inverters',
    description: 'DC to AC power conversion equipment',
    slug: 'inverters',
    icon: 'zap',
    productCount: 12,
    status: 'active',
    createdAt: '2024-02-01',
    updatedAt: '2024-03-05',
  },
  {
    id: 4,
    name: 'Controllers',
    description: 'Charge controllers and system management',
    slug: 'controllers',
    icon: 'cpu',
    productCount: 15,
    status: 'active',
    createdAt: '2024-02-10',
    updatedAt: '2024-03-12',
  },
  {
    id: 5,
    name: 'Accessories',
    description: 'Cables, connectors, and mounting hardware',
    slug: 'accessories',
    icon: 'package',
    productCount: 32,
    status: 'active',
    createdAt: '2024-02-15',
    updatedAt: '2024-03-15',
  },
  {
    id: 6,
    name: 'Tools',
    description: 'Installation and maintenance tools',
    slug: 'tools',
    icon: 'wrench',
    productCount: 8,
    status: 'inactive',
    createdAt: '2024-02-20',
    updatedAt: '2024-03-01',
  },
];

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Filter and paginate categories
  const filteredCategories = useMemo(() => {
    return categories.filter(category =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.slug.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [categories, searchTerm]);

  const totalPages = Math.ceil(filteredCategories.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedCategories = filteredCategories.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleCreateCategory = async (data: CategoryFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newCategory: Category = {
        id: Math.max(...categories.map(c => c.id)) + 1,
        ...data,
        productCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setCategories(prev => [...prev, newCategory]);
      setIsFormModalOpen(false);
    } catch (error) {
      console.error('Error creating category:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateCategory = async (data: CategoryFormData) => {
    if (!editingCategory) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setCategories(prev => prev.map(category =>
        category.id === editingCategory.id
          ? { ...category, ...data, updatedAt: new Date().toISOString() }
          : category
      ));

      setIsFormModalOpen(false);
      setEditingCategory(null);
    } catch (error) {
      console.error('Error updating category:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId: number) => {
    if (!confirm('Are you sure you want to delete this category?')) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setCategories(prev => prev.filter(category => category.id !== categoryId));
    } catch (error) {
      console.error('Error deleting category:', error);
    }
  };

  const openCreateModal = () => {
    setEditingCategory(null);
    setIsFormModalOpen(true);
  };

  const openEditModal = (category: Category) => {
    setEditingCategory(category);
    setIsFormModalOpen(true);
  };

  const closeModal = () => {
    setIsFormModalOpen(false);
    setEditingCategory(null);
  };

  return (
    <AdminLayout
      title="Categories"
      subtitle="Organize your product catalog"
    >
      <div className="space-y-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Input
              type="search"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
              className="pl-10"
            />
          </div>
          <Button variant="default" className="whitespace-nowrap" onClick={openCreateModal}>
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        </div>

        {/* Categories Table */}
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedCategories.map((category) => {
                  const IconComponent = iconMap[category.icon as keyof typeof iconMap];

                  return (
                    <TableRow key={category.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-lg bg-neutral-100 flex items-center justify-center">
                            {IconComponent && <IconComponent className="h-5 w-5 text-neutral-600" />}
                          </div>
                          <div>
                            <div className="font-medium text-neutral-900">{category.name}</div>
                            <div className="text-sm text-neutral-500">ID: {category.id}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs">
                          <p className="text-sm text-neutral-600 line-clamp-2">
                            {category.description}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="px-2 py-1 bg-neutral-100 rounded text-sm font-mono">
                          {category.slug}
                        </code>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm font-medium">
                          {category.productCount}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={category.status === 'active' ? 'default' : 'secondary'}
                          size="sm"
                        >
                          {category.status === 'active' ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditModal(category)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteCategory(category.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>

            {/* Pagination */}
            {filteredCategories.length > 0 && (
              <div className="border-t border-neutral-200 px-6 py-4">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  itemsPerPage={itemsPerPage}
                  totalItems={filteredCategories.length}
                  onItemsPerPageChange={setItemsPerPage}
                />
              </div>
            )}

            {/* Empty State */}
            {filteredCategories.length === 0 && (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-neutral-900 mb-2">
                  {searchTerm ? 'No categories found' : 'No categories yet'}
                </h3>
                <p className="text-neutral-600 mb-4">
                  {searchTerm
                    ? 'Try adjusting your search terms.'
                    : 'Get started by creating your first category.'
                  }
                </p>
                {!searchTerm && (
                  <Button onClick={openCreateModal}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Category
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Category Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Categories</p>
                  <p className="text-2xl font-bold text-neutral-900">{categories.length}</p>
                </div>
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Products</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {categories.reduce((sum, cat) => sum + cat.productCount, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-secondary-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Active Categories</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {categories.filter(cat => cat.status === 'active').length}
                  </p>
                </div>
                <div className="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-success-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Category Form Modal */}
      <Modal
        isOpen={isFormModalOpen}
        onClose={closeModal}
        title={editingCategory ? 'Edit Category' : 'Create New Category'}
        description={editingCategory ? 'Update the category details below.' : 'Fill in the details to create a new category.'}
        size="lg"
      >
        <CategoryForm
          initialData={editingCategory || undefined}
          onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory}
          onCancel={closeModal}
          isLoading={isLoading}
          mode={editingCategory ? 'edit' : 'create'}
        />
      </Modal>
    </AdminLayout>
  );
}
